; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino
lib_deps = 
	adafruit/Adafruit Unified Sensor@^1.1.9
	adafruit/DHT sensor library@^1.4.4
	vshymanskyy/TinyGSM@0.10.9
	knolleary/PubSubClient@^2.8
	wnatth3/WiFiManager@^2.0.16-rc.2
	thingsboard/ThingsBoard@0.6
    bblanchon/Ard<PERSON><PERSON><PERSON><PERSON>@6.18.0
	madhephaestus/ESP32Servo@^3.0.5

