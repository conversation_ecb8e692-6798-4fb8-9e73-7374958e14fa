#include <Arduino.h>
#include <WiFiManager.h>
#include <WiFi.h>
#include <WiFiClient.h>
#include <ESP32Servo.h>

Servo Servo1;
// Data wire is plugged i


 #include <ArduinoJson.h>

#include <ThingsBoard.h>
#include "driver/adc.h"
#include <esp_wifi.h>

int My_switch_variable;

#include <Adafruit_Sensor.h>
#include <DHT.h>
#include <DHT_U.h>
// Data wire is plugged into port 2 on the Arduino
#define trigPin            16
#define echoPin            17

#define trigPin_2          18
#define echoPin_2          19

#define buzzer             12
#define servo_pin          13
#define battery_pin        35
#define network            21
#define cam_alert           4



#define DHTPIN              27     // Digital pin connected to the DHT sensor 

#define DHTTYPE    DHT11     // DHT 22 (AM2302)
DHT_Unified dht(DHTPIN, DHTTYPE);
# define smell_pin 34  

int timer;
int timer_2;
float binLevel_1;
float binLevel_2;
float cal_value;

float objDetect_1;
float objDetectl_2;

int ctrl=0;
int battery_sense=0;
float battery_volt=0.00;
const char* ssid = "";
const char* password = "";

#define TOKEN "BRuVv1ypGBwBV0cm0Rzv" // Thingsboard token
 
#define THINGSBOARD_SERVER "demo.thingsboard.io" // Thingsboard server

// Baud rate for debug /
#define SERIAL_DEBUG_BAUD 9600

WiFiClient espClient;

// Initialize ThingsBoard instance
ThingsBoard tb(espClient);


RPC_Response processSwitchChange(const RPC_Data &data)
{
  Serial.println("Received the set switch method");
  // Process data
  int pin_state = 0;
  My_switch_variable = ((int) data);

  Serial.print("Example switch state: ");
  Serial.println(My_switch_variable);
  digitalWrite(12, !My_switch_variable); // toggle inverter relay
  pin_state=digitalRead(12);
  Serial.println(pin_state);
  return RPC_Response(NULL,data);
}

RPC_Callback callbacks[] = {
  { "inverter.set",processSwitchChange},
};
// Set to true, if modem is connected
bool modemConnected = false;
bool subscribed = false;
unsigned long timestamp;


void setup_wifi() {
  Serial.begin(9600);
  delay(10);
  Serial.println();
  Serial.print("Connecting to ");
  Serial.println(ssid);

  WiFi.begin(ssid, password);

  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
    digitalWrite(network,LOW);

  }

  randomSeed(micros());

  Serial.println("");
  Serial.println("WiFi connected");
  delay(50);
  digitalWrite(buzzer,HIGH);
  delay(500);
  digitalWrite(buzzer,LOW);
  delay(50);
  digitalWrite(buzzer,HIGH);
  delay(500);
  digitalWrite(buzzer,LOW);
  Serial.println("IP address: ");
  Serial.println(WiFi.localIP());
  digitalWrite(network,HIGH);
}


void setup()
{
	Serial.begin(9600);
  dht.begin();
  sensor_t sensor;
  dht.temperature().getSensor(&sensor);
  dht.humidity().getSensor(&sensor);
  pinMode(smell_pin,INPUT);
  pinMode(battery_pin ,INPUT);
  pinMode(trigPin,OUTPUT);
  pinMode(echoPin,INPUT);
  pinMode(trigPin_2,OUTPUT);
  pinMode(echoPin_2,INPUT);
  pinMode(buzzer,OUTPUT);
  pinMode(cam_alert ,OUTPUT);
  pinMode(network,OUTPUT);

  Servo1.attach(servo_pin);

  Servo1.write(90);
  delay(300); 
  WiFiManager wm;
   
    bool res;
    res = wm.autoConnect("AutoConnectAP","password"); // password protected ap

    if(!res) {
        Serial.println("Failed to connect");
        // ESP.restart();
    } 
    else {
        //if you get here you have connected to the WiFi    
      
        Serial.println("connected...");
    }
  
  setup_wifi();

}

void control(){

   Servo1.write(0);
   delay(5000);
   Servo1.write(90);
   delay(300); 
   digitalWrite(cam_alert,HIGH);
   delay(3000); 


}


void loop()
{
	
   digitalWrite(trigPin_2,LOW);
   delayMicroseconds(2);
   digitalWrite(trigPin_2,HIGH);
   delayMicroseconds(2);
   digitalWrite(trigPin_2,LOW);
  

   timer_2= pulseIn(echoPin_2,HIGH);
   objDetect_1= timer_2*0.034/2;
   
   Serial.print("user_sense :" );
   Serial.println(objDetect_1);
   
    if(objDetect_1<=30){
   digitalWrite(buzzer,HIGH);
   delay(300);
   digitalWrite(buzzer,LOW);
   control();
  
   }
   else if (binLevel_2<=15 && objDetect_1<=30) {
   Servo1.write(90);
   delay(300); 
   }

   else{
   Servo1.write(90);
   delay(300); 
    digitalWrite(cam_alert,LOW);
    }
  


  digitalWrite(trigPin,LOW);
   delayMicroseconds(2);
   digitalWrite(trigPin,HIGH);
   delayMicroseconds(2);
   digitalWrite(trigPin,LOW);
  

   timer= pulseIn(echoPin,HIGH);
   binLevel_1= timer*0.034*0.5;
   cal_value=(binLevel_1*100)/19.20;
   binLevel_2=100-cal_value;
   Serial.print("bin level: ");
   Serial.println( binLevel_2);
   Serial.print("bin size: ");
   Serial.println(  binLevel_1);
   delay(20);

   if (binLevel_2>=85)
  {
  
   digitalWrite(buzzer,HIGH);
   delay(200);
   digitalWrite(buzzer,LOW);
   delay(2000);
   digitalWrite(buzzer,HIGH);
   delay(200);
   digitalWrite(buzzer,LOW);
   
   }
    else 
  {
    digitalWrite(buzzer,LOW);
   
  }

  int smell= analogRead(smell_pin);
  //Serial.println(smell);
  int odour= (smell*100)/4095;
  Serial.print("concentration: ");
  Serial.println(odour);
  delay(200);

  // Get temperature event and print its value.
  sensors_event_t event;
  dht.temperature().getEvent(&event);
  float temp_value = event.temperature;
  if (isnan(event.temperature)) {
    Serial.println(F("Error reading temperature!"));
  }
  else {
    Serial.print(F("Temperature: "));
    Serial.print(temp_value);
    Serial.println(F("°C"));
  }
  // Get humidity event and print its value.
  dht.humidity().getEvent(&event);
  float humid_value= event.relative_humidity;
  if (isnan(event.relative_humidity)) {
    Serial.println(F("Error reading humidity!"));
  }
  else {
    Serial.print(F("Humidity: "));
    Serial.print(humid_value);
    Serial.println(F("%"));
}

battery_sense= analogRead(battery_pin);
Serial.println(battery_sense);

battery_volt=(battery_sense*195.7)/4095;
Serial.print("battery volt :"  );
Serial.println(battery_volt);




  if (!tb.connected())
  {
    // Connect to the ThingsBoard
    Serial.print("Connecting to: ");
    Serial.print(THINGSBOARD_SERVER);
    Serial.print(" with token ");
    Serial.println(TOKEN);
    if (!tb.connect(THINGSBOARD_SERVER, TOKEN))
    {
      Serial.println("Failed to connect");
    }
  }

  Serial.println("Sending data...");
  

    if (!subscribed) {
    Serial.println("Subscribing for RPC...");

    
    if (!tb.RPC_Subscribe(callbacks, 1)) {
      Serial.println("Failed to subscribe for RPC");
      return;
    }

    Serial.println("Subscribe done");
    subscribed = true;
  }
  if (millis() - timestamp > 1000) {
  tb.sendTelemetryFloat("bin level",binLevel_2);
  tb.sendTelemetryFloat("temperature",temp_value);
  tb.sendTelemetryFloat("humidity",humid_value);
  tb.sendTelemetryFloat("concentration",odour);
  tb.sendTelemetryFloat("battery",battery_volt);
  
  timestamp = millis();
  }
  tb.loop();


    }
