; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32cam]
platform = espressif32
board = esp32cam
framework = arduino
lib_deps = 
	densaugeo/base64@^1.4.0
	espressif/esp32-camera@^2.0.4
	wnatth3/WiFiManager@^2.0.16-rc.2
