const ds_server_url = "http://51.21.95.248"
const _ds_server_url = "http://172.31.35.184"
const hardware_server_url = "https://demo.thingsboard.io"
document.addEventListener('DOMContentLoaded', function() {
    // Function to fetch and populate temperature, humidity, battery, and waste level
    async function fetchSensorData() {
      try {
        const response = await fetch(hardware_server_url + '/api/plugins/telemetry/DEVICE/3b28e5b0-251d-11ef-a435-ab3a1d535f3e/values/timeseries?useStrictDataTypes=false', {
            headers: {
                'accept': 'application/json',
                'X-Authorization': 'Bearer Bearer eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZAKRS8Xbm8JrBfzy4Mi44jCz4omUvGwk_oQhztzde_BiCQKcGBTRFUOPjvZxuxULA4jq_VkfYsHFjMYl9ufbow'
            }
        });
        const data = await response.json();
  
        // Update the top metrics
        document.getElementById('binLevel').textContent = `${data["bin level"][0].value < 0 ? 100 : Math.round(data["bin level"][0]?.value)}%`;
        document.getElementById('temperature').textContent = `${data.temperature[0].value}°C`;
        document.getElementById('humidity').textContent = `${data.humidity[0].value}%`;
        document.getElementById('battery').textContent = `${data.battery[0].value > 100 ? 'charging' : Math.round(data.battery[0].value) + "%" }`;
      } catch (error) {
        console.error('Error fetching sensor data:', error);
      }
    }
  
    // Function to fetch and populate predictions and images
    async function fetchPredictionsData() {
        try {
          const response = await fetch(ds_server_url + '/predictions');
          const data = await response.json();
          
          // Update the predictions list
          const itemsContainer = document.getElementById('items-container');
          itemsContainer.innerHTML = '';
      
          data.forEach(prediction => {
            const itemDiv = document.createElement('div');
            itemDiv.classList.add('item1');
      
            // Create image thumbnail and link for modal
            const imageCell = document.createElement('h3');
            const imageThumbnail = document.createElement('img');
            imageThumbnail.src = prediction.url;
            imageThumbnail.classList.add('thumbnail');
            imageThumbnail.alt = "Thumbnail";
            imageThumbnail.addEventListener('click', () => openModal(prediction.url));
      
            imageCell.appendChild(imageThumbnail);
      
            // Create the rest of the cells
            const predictionCell = document.createElement('h3');
            predictionCell.classList.add('t-op-nextlvl');
            predictionCell.textContent = prediction.prediction;
      
            const timeCell = document.createElement('h3');
            timeCell.classList.add('t-op-nextlvl');
            
            // Format the file name as time
            const formattedTime = prediction.file_name.replace(/(\d{4})(\d{2})(\d{2})-(\d{2})(\d{2})(\d{2})\.jpg/, '$4:$5:$6 $2/$3/$1');
            timeCell.textContent = formattedTime;
      
            // Append cells to itemDiv
            itemDiv.appendChild(imageCell);
            itemDiv.appendChild(predictionCell);
            itemDiv.appendChild(timeCell);
      
            // Append itemDiv to itemsContainer
            itemsContainer.appendChild(itemDiv);
          });
        } catch (error) {
          console.error('Error fetching predictions data:', error);
        }
      }
  
    // Modal Logic
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('imgModalContent');
    const closeModal = document.getElementsByClassName('close')[0];
  
    function openModal(imageUrl) {
      modal.style.display = 'block';
      modalImg.src = imageUrl;
    }
  
    closeModal.onclick = function() {
      modal.style.display = 'none';
    };
  
    window.onclick = function(event) {
      if (event.target == modal) {
        modal.style.display = 'none';
      }
    };
  
    // Initial data fetches
    fetchSensorData();
    fetchPredictionsData();
  
    // Set interval to auto-refresh data every 3 minutes (180000 milliseconds)
    setInterval(() => {
      fetchSensorData();
    //   fetchPredictionsData();
    }, 3000); // 3 sec in milliseconds
  });
  