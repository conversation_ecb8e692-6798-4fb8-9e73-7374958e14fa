self.addEventListener('install', event => {
    event.waitUntil(
        caches.open('smart-bin-cache').then(cache => {
            return cache.addAll([
                '/dashboard',
                '/static/main.css',
                '/static/app.js',
                '/static/icons/icon-192x192.png',
                '/static/icons/icon-512x512.png'
                // Add other assets you want to cache
            ]);
        })
    );
});

self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request).then(response => {
            return response || fetch(event.request);
        })
    );
});
