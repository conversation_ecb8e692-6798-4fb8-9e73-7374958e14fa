import os
import zipfile
import tempfile
from datetime import datetime
from fastai.vision.all import *
from PIL import Image
import matplotlib.pyplot as plt
import shutil
import json
import threading
import time
from flask import Flask, jsonify, send_from_directory
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
import logging
from flask_cors import CORS
# Global variable to track if a thread is running
prediction_thread_running = False
thread_lock = threading.Lock()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Function to authenticate and create the Google Drive client
def authenticate_drive():
    service_account_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'service_account.json')
    credentials = service_account.Credentials.from_service_account_file(
        service_account_file,
        scopes=['https://www.googleapis.com/auth/drive']
    )
    service = build('drive', 'v3', credentials=credentials)
    logger.info("Authenticated Google Drive service.")
    return service

# Function to fetch images from the date folder in Google Drive and return their URLs
def fetch_images_from_drive(service, date_folder):
    root_folder_name = 'SMART_BIN-CAM'
    logger.info(f"Fetching images from drive, looking for folder: {date_folder}")

    # Get the root folder ID
    results = service.files().list(
        q=f"name='{root_folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        fields="files(id, name)"
    ).execute()
    root_folder = results.get('files', [])

    if not root_folder:
        logger.error(f"Root folder '{root_folder_name}' not found in Google Drive.")
        raise ValueError(f"Root folder '{root_folder_name}' not found in Google Drive")

    root_folder_id = root_folder[0]['id']

    # Get the date folder ID
    results = service.files().list(
        q=f"'{root_folder_id}' in parents and name='{date_folder}' and mimeType='application/vnd.google-apps.folder' and trashed=false",
        fields="files(id, name)"
    ).execute()
    date_folder = results.get('files', [])

    if not date_folder:
        logger.error(f"Date folder '{date_folder}' not found in '{root_folder_name}'.")
        raise ValueError(f"Date folder '{date_folder}' not found in '{root_folder_name}'")

    date_folder_id = date_folder[0]['id']

    # List all files in the date folder
    results = service.files().list(
        q=f"'{date_folder_id}' in parents and trashed=false",
        fields="files(id, name)",
        pageSize=1000  # Increase the page size to ensure all files are fetched
    ).execute()
    file_list = results.get('files', [])

    # Check if there are more files to fetch
    while 'nextPageToken' in results:
        page_token = results['nextPageToken']
        results = service.files().list(
            q=f"'{date_folder_id}' in parents and trashed=false",
            fields="files(id, name)",
            pageSize=1000,
            pageToken=page_token
        ).execute()
        file_list.extend(results.get('files', []))

    temp_dir = tempfile.mkdtemp(dir=os.getcwd())
    urls = []
    # print(file_list)
    for file in file_list:
        try:
            request = service.files().get_media(fileId=file['id'])
            file_path = os.path.join(temp_dir, file['name'])
            print(file_path,'starting')
            with open(file_path, 'wb') as fh:
                downloader = MediaIoBaseDownload(fh, request)
                done = False
                while not done:
                    status, done = downloader.next_chunk()
            urls.append({"file_name": file['name'], "url": f"https://drive.google.com/uc?id={file['id']}"})
        except Exception as e:
            print("Error downloading", e)
    # print(urls,'urls<<<<')
    logger.info(f"Fetched {len(file_list)} files from date folder '{date_folder}'.")
    return temp_dir, urls, len(file_list)
# Function to save predictions and URLs to a JSON file
def save_predictions_to_file(predictions, urls, output_file="predictions.json"):
    print(predictions,urls,'final')
    result = [{"file_name": url["file_name"], "url": url["url"], "prediction": pred[1]} for url, pred in zip(urls, predictions)]
    with open(output_file, 'w') as f:
        json.dump(result, f, indent=4)
    logger.info(f"Saved predictions to {output_file}.")

# Function to check for new files in the folder
def check_for_new_files(service, date_folder, previous_file_count):
    _, _, current_file_count = fetch_images_from_drive(service, date_folder)
    return current_file_count > previous_file_count

# Function to install fastai and other dependencies (if needed)
def install_dependencies():
    os.system("pip install fastai")
    logger.info("Installed fastai and other dependencies.")

# Function to clone the repository if it doesn't exist
def clone_repository(repo_url='https://github.com/rootstrap/fastai-waste-classifier', repo_name='fastai-waste-classifier'):
    if not os.path.exists(repo_name):
        os.system(f'git clone {repo_url}')
        logger.info(f"Cloned repository from {repo_url}.")
    os.chdir(repo_name)

# Function to load the pre-trained model
def load_model(model_path='/home/<USER>/final_year_project/backend_scripts/fastai-waste-classifier/result-resnet50.pkl'):
    logger.info("Loading model learner from resnet")
    return load_learner(model_path)

# Function to get predictions from the model
def get_predictions(model, folder_path):
    predictions = []
    for img_file in os.listdir(folder_path):
        logger.info(f"Processing file: {img_file}")
        img_path = os.path.join(folder_path, img_file)
        img = PILImage.create(img_path)
        pred, _, probs = model.predict(img)
        predictions.append((img_file, pred, probs.max().item()))
        logger.info(f"Prediction for file {img_file}: {pred}")
    return predictions

# Function to display images with predictions
def display_predictions(folder_path, predictions):
    rows = round(len(predictions) / 5)
    _, axs = plt.subplots(rows, 5, figsize=(20, 20))
    axs = axs.flatten()

    for img, ax, p in zip(os.listdir(folder_path), axs, predictions):
        image = Image.open(os.path.join(folder_path, img))
        ax.set_title(p[1])
        ax.imshow(image)
        ax.set_xticks([])
        ax.set_yticks([])

    plt.show()

# Background thread function to run the image processing script
def run_trigger_script():
    global prediction_thread_running
    try:
        with thread_lock:
            if prediction_thread_running:
                logger.info("Prediction thread is already running. Exiting run_trigger_script.")
                return
            prediction_thread_running = True

        logger.info("Starting trigger script execution.")
        # install_dependencies()
        # clone_repository()

        service = authenticate_drive()

        date_folder = datetime.today().strftime('%Y%m%d')
        logger.info(f"New trigger date folder: {date_folder}")
        logger.info("Fetching trigger images...")
        
        temp_dir, urls, previous_file_count = fetch_images_from_drive(service, date_folder)

        model = load_model()

        logger.info("Predicting...")
        predictions = get_predictions(model, temp_dir)
        logger.info("Saving trigger predictions...")
        save_predictions_to_file(predictions, urls)
        logger.info("Trigger script execution complete.")
    finally:
        with thread_lock:
            prediction_thread_running = False


def run_predictions_in_background():
    """Function to run predictions and handle processing in a separate thread."""
    run_trigger_script()  # This will execute the prediction logic as intended


def run_script(stop_event):
    logger.info("Starting script execution.")
    install_dependencies()
    clone_repository()

    service = authenticate_drive()

    date_folder = datetime.today().strftime('%Y%m%d')
    logger.info(f"New date folder: {date_folder}")
    logger.info("Fetching images...")
    
    temp_dir, urls, previous_file_count = fetch_images_from_drive(service, date_folder)

    model = load_model()

    logger.info("Starting event loop.")
    while not stop_event.is_set():
        logger.info("Predicting...")
        predictions = get_predictions(model, temp_dir)
        logger.info("Saving predictions...")
        save_predictions_to_file(predictions, urls)
        logger.info("Sleeping for 60 seconds...")
        time.sleep(60)
        if check_for_new_files(service, date_folder, previous_file_count):
            shutil.rmtree(temp_dir)
            logger.info("New files detected. Fetching updated files...")
            temp_dir, urls, previous_file_count = fetch_images_from_drive(service, date_folder)
            logger.info("Fetched new files.")

# Flask API setup
app = Flask(__name__)

# Enable CORS for all routes
CORS(app)

@app.route('/predictions', methods=['GET'])
def get_predictionss():
    try:
        # Check if 'fastai-waste-classifier' directory exists
        fastai_dir = '/home/<USER>/final_year_project/backend_scripts/fastai-waste-classifier'
        output_path = None
        
        try:
            fastai_dir = '/home/<USER>/final_year_project/backend_scripts'
            output_path = os.path.join(fastai_dir, 'predictions.json')
            
            if not os.path.isfile(output_path):
                logger.error(f"File not found: {output_path}")
                raise ValueError("File not found: {output_path}")
        except:
            fastai_dir = os.path.join(os.getcwd())
            output_path = os.path.join(fastai_dir, 'predictions.json')
            
        with open(output_path, 'r') as f:
            data = json.load(f) 
        logger.info("Loaded predictions data.")
        global prediction_thread_running
        try:
            with thread_lock:
                if prediction_thread_running:
                    return jsonify(data)

            logger.info('Starting a new prediction thread.')
            prediction_thread = threading.Thread(target=run_trigger_script)
            prediction_thread.start()
        except:
            print('An error occurred')
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error loading predictions data: {e}")
        return jsonify({"message": "Something went wrong"}), 500
    
@app.route('/trigger_predictions', methods=['GET'])
def trigger_predictions():
    try:
        # run_trigger_script()
        fastai_dir = '/home/<USER>/final_year_project/backend_scripts/fastai-waste-classifier'
        output_path = os.path.join(fastai_dir, 'predictions.json')
        
        if not os.path.isfile(output_path):
            logger.error(f"File not found: {output_path}")
            return jsonify([]), 200
        
        with open(output_path, 'r') as f:
            data = json.load(f) 
        logger.info("Loaded trigger predictions data.")
        run_trigger_script()
        return jsonify(data)
    except Exception as e:
        logger.error(f"Error loading trigger predictions data: {e}")
        return jsonify({
            "message": "something went wrong"
        })

@app.route('/health', methods=['GET'])
def get_health():
    return jsonify({
        "message": "up and running"
    })
    
@app.route('/static/<path:filename>')
def custom_static(filename):
    return send_from_directory('static', filename)

@app.route('/dashboard', methods=['GET'])
def serve_dashboard():
    try:
        html_dir = os.path.join(os.getcwd(), '..', 'app')
        return send_from_directory(html_dir, 'main.html')
    except Exception as e:
        logger.error(f"Error serving dashboard: {e}")
        return jsonify({"message": str(e)}), 500

if __name__ == '__main__':
    logger.info('Main program started.')
    # stop_event = threading.Event()
    logger.info('scripting thread')
    # script_thread = threading.Thread(target=run_script, args=(stop_event,))
    # script_thread.start()

    try:
        app.run(host='0.0.0.0', port=5020)
    finally:
        # stop_event.set()
        # script_thread.join()
        print("ended")