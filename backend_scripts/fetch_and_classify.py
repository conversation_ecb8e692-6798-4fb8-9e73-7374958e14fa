import os
import zipfile
import tempfile
from datetime import datetime
from fastai.vision.all import *
from PIL import Image
import matplotlib.pyplot as plt
import shutil
from pydrive.auth import GoogleAuth
from pydrive.drive import GoogleDrive
import time
import json

# Authenticate and create the PyDrive client
def authenticate_drive():
    gauth = GoogleAuth()
    gauth.LocalWebserverAuth()  # Creates local webserver and auto-handles authentication
    drive = GoogleDrive(gauth)
    return drive

# Fetch images from the date folder in Google Drive and return their URLs
def fetch_images_from_drive(drive, date_folder):
    root_folder_name = 'smart_waste_bin'
    
    root_folder = drive.ListFile({'q': f"title='{root_folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"}).GetList()
    if not root_folder:
        raise ValueError(f"Root folder '{root_folder_name}' not found in Google Drive")
    
    root_folder_id = root_folder[0]['id']
    
    date_folder = drive.ListFile({'q': f"'{root_folder_id}' in parents and title='{date_folder}' and mimeType='application/vnd.google-apps.folder' and trashed=false"}).GetList()
    if not date_folder:
        raise ValueError(f"Date folder '{date_folder}' not found in '{root_folder_name}'")
    
    date_folder_id = date_folder[0]['id']
    
    file_list = drive.ListFile({'q': f"'{date_folder_id}' in parents and trashed=false"}).GetList()
    
    temp_dir = tempfile.mkdtemp(dir=os.getcwd())
    urls = []
    for file in file_list:
        file_path = os.path.join(temp_dir, file['title'])
        file.GetContentFile(file_path)
        urls.append({"file_name": file['title'], "url": f"https://drive.google.com/uc?id={file['id']}"})
    
    return temp_dir, urls, len(file_list)

# Save predictions and URLs to a JSON file
def save_predictions_to_file(predictions, urls, output_file="predictions.json"):
    result = [{"file_name": url["file_name"], "url": url["url"], "prediction": pred[1]} for url, pred in zip(urls, predictions)]
    with open(output_file, 'w') as f:
        json.dump(result, f, indent=4)

# Check for new files in the folder
def check_for_new_files(drive, date_folder, previous_file_count):
    _, _, current_file_count = fetch_images_from_drive(drive, date_folder)
    return current_file_count > previous_file_count

# Function to install fastai and other dependencies (if needed)
def install_dependencies():
    os.system("pip install fastai==2.5.3")

# Clone the repository if it doesn't exist
def clone_repository(repo_url='https://github.com/rootstrap/fastai-waste-classifier', repo_name='fastai-waste-classifier'):
    if not os.path.exists(repo_name):
        os.system(f'git clone {repo_url}')
    os.chdir(repo_name)

# Load the pre-trained model
def load_model(model_path='result-resnet50.pkl'):
    return load_learner(model_path)

# Process images and predict
def process_images(model, folder_path):
    predictions = utils.get_predictions(model, folder_path)
    return predictions

# Function to display images with predictions
def display_predictions(folder_path, predictions):
    rows = round(len(predictions) / 5)
    _, axs = plt.subplots(rows, 5, figsize=(20, 20))
    axs = axs.flatten()

    for img, ax, p in zip(os.listdir(folder_path), axs, predictions):
        image = Image.open(os.path.join(folder_path, img))
        ax.set_title(p[1])
        ax.imshow(image)
        ax.set_xticks([])
        ax.set_yticks([])

    plt.show()

def main():
    install_dependencies()
    clone_repository()

    drive = authenticate_drive()

    date_folder = datetime.today().strftime('%Y%m%d')

    temp_dir, urls, previous_file_count = fetch_images_from_drive(drive, date_folder)

    model = load_model()

    while True:
        predictions = process_images(model, temp_dir)
        # display_predictions(temp_dir, predictions)
        save_predictions_to_file(predictions, urls)

        time.sleep(60)
        if check_for_new_files(drive, date_folder, previous_file_count):
            shutil.rmtree(temp_dir)
            temp_dir, urls, previous_file_count = fetch_images_from_drive(drive, date_folder)

if __name__ == "__main__":
    main()
